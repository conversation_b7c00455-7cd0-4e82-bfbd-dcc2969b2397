<?php

  use classes\ApiResponse;

  trait IndufastCalendarEventApiTrait {

    public function executeEventCreate(): void {
      $event = new IndufastCalendarEvent();
      $event->fill($this->data)->validateFillable();

      try {
        if (!is_null($this->data['employee_ids'] ?? null)) {
          $event->updateEmployees();
        }
        $event->save();

        ApiResponse::sendResponseOK('Event saved', $event);
      }
      catch (\Exception $e) {
        logToFile(__CLASS__, var_export($e->getMessage(), true));
        ApiResponse::sendResponseError('Unknown error occured while saving.');
      }
    }

    public function executeEventUpdate(): void {
      if (!$event = IndufastCalendarEvent::find_by_id($_GET['id'] ?? null)) {
        ApiResponse::sendResponseNotFound('Event not found');
      }
      $event->fill($this->data)->validateFillable();

      try {
        if (!is_null($this->data['employee_ids'] ?? null)) {
          $event->updateEmployees();
        }
        $event->save();
        ApiResponse::sendResponseOK('Event saved', $event);
      }
      catch (\Exception $e) {
        logToFile(__CLASS__, var_export($e->getMessage(), true));
        ApiResponse::sendResponseError('Unknown error occured while saving.', (DEVELOPMENT) ? $e->getMessage() : '');
      }
    }

    public function executeEventDelete(): void {
      if (!$event = IndufastCalendarEvent::find_by_id($_GET['id'] ?? null)) {
        ApiResponse::sendResponseNotFound('Event not found');
      }

      try {
        $event->destroy();
        ApiResponse::sendResponseOK('Event deleted', $event);
      }
      catch (\Exception $e) {
        logToFile(__CLASS__, var_export($e->getMessage(), true));
        ApiResponse::sendResponseError('Unknown error occured while deleting.');
      }
    }

    public function executeEventGetGoogleFiles(): void {
      if (!$event = IndufastCalendarEvent::find_by_id($_GET['id'] ?? null)) {
        ApiResponse::sendResponseNotFound('Calendar event not found');
      }

      try {
        $files = $event->getGoogleFiles();
        ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, $files);
      }
      catch (\Exception $e) {
        logToFile(__CLASS__, var_export($e->getMessage(), true));
        ApiResponse::sendResponseError('Unknown error occured while retrieving files.');
      }
    }

    public function executeEventBatchSave(): void {
      try {
        foreach ($this->data as &$eventData) {
          $event = $eventData['id'] ? IndufastCalendarEvent::find_by_id($eventData['id']) : new IndufastCalendarEvent();
          $errors = $event->fill($eventData)->getValidationErrors();
          $eventData['errors'] = $errors ?: [];
          if ($errors) {
            continue;
          }

          if (!is_null($eventData['employee_ids'] ?? null)) {
            $event->updateEmployees();
          }
          $event->save();
        }
        ApiResponse::sendResponseOK('Events saved', $this->data);
      }
      catch (\Exception $e) {
        logToFile(__CLASS__, var_export($e->getMessage(), true));
        ApiResponse::sendResponseError('Unknown error occured while saving.');
      }
    }

    public function executeEventBatchDelete(): void {
      try {
        foreach ($this->data as $eventId) {
          $event = IndufastCalendarEvent::find_by_id($eventId);
          $event->destroy();
        }
        ApiResponse::sendResponseOK('Events deleted', $this->data);
      }
      catch (\Exception $e) {
        logToFile(__CLASS__, var_export($e->getMessage(), true));
        ApiResponse::sendResponseError('Unknown error occured while deleting.');
      }
    }

    public function executeEventBatchGetGoogleFiles(): void {
      try {
        foreach ($this->data as &$eventData) {
          $event = IndufastCalendarEvent::find_by_id($eventData['id']);
          $eventData['google_drive_files'] = $event->getGoogleFiles();
        }
        ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, $this->data);
      } catch (\Exception $e) {
        logToFile(__CLASS__, var_export($e->getMessage(), true));
        ApiResponse::sendResponseError('Unknown error occured while retrieving files.');
      }
    }
  }