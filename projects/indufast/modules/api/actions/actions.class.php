<?php

  use classes\ApiResponse;
  use classes\DatesTrait;
  use classes\TimesTrait;

  require_once 'ApiAuthenticationTrait.php';
  require_once 'IndufastProjectApiTrait.php';
  require_once 'IndufastEmployeeApiTrait.php';
  require_once 'IndufastCalendarEventApiTrait.php';
  require_once 'IndufastCalendarEventEmployeeApiTrait.php';
  require_once 'IndufastEmployeeAvailabilityApiTrait.php';
  require_once 'IndufastChangelogApiTrait.php';
  require_once 'IndufastGoogleApiTrait.php';
  require_once 'IndufastWorkdayApiTrait.php';
  require_once 'IndufastWorkdayLineApiTrait.php';

  class apiIndufastActions extends apiActions {

    use ApiAuthenticationTrait;
    use IndufastProjectApiTrait;
    use IndufastEmployeeApiTrait;
    use IndufastCalendarEventApiTrait;
    use IndufastCalendarEventEmployeeApiTrait;
    use IndufastEmployeeAvailabilityApiTrait;
    use IndufastChangelogApiTrait;
    use IndufastGoogleApiTrait;
    use ValidationTrait;
    use IndufastWorkdayApiTrait;
    use IndufastWorkdayLineApiTrait;
    use TimesTrait;
    use DatesTrait;

    const array ACTION_PERMISSION = [
      'login'  => null,
      'logout' => null,
      'googleAuthUrl' => null,
      'googleAuthCallback' => null,

      'workdayLineCreate' => 'M_ACCREDIS',

      'getHolidays'               => 'M_ACCREDIS',
      'workdayList'               => 'M_ACCREDIS',
      'workdayExport'             => 'M_ACCREDIS',
      'workdayCalculate'          => 'M_ACCREDIS',
      'workdaySummary'            => 'M_ACCREDIS',
      'workdayCreate'             => 'M_ACCREDIS',
      'workdayUpdate'             => 'M_ACCREDIS',
      'workdaySpecialHoursList'   => 'M_ACCREDIS',
      'workdaySpecialHoursCreate' => 'M_ACCREDIS',
      'workdaySpecialHoursUpdate' => 'M_ACCREDIS',
      'workdaySpecialHoursDelete' => 'M_ACCREDIS',

      'projectCreate' => 'M_ADMINISTER_PROJECTS',
      'projectUpdate' => 'M_ADMINISTER_PROJECTS',
      'projectList'   => 'M_VIEWER',
      'projectDelete' => 'M_ADMINISTER_PROJECTS',

      'employeeCreate' => 'M_ADMINISTER_EMPLOYEES',
      'employeeUpdate' => 'M_ADMINISTER_EMPLOYEES',
      'employeeGet'    => 'M_VIEWER',
      'employeeList'   => 'M_VIEWER',
      'employeeDelete' => 'M_ADMINISTER_EMPLOYEES',
      'eventGetGoogleFiles' => 'M_VIEWER',

      'eventCreate' => 'M_ADMINISTER_PROJECTS',
      'eventUpdate' => 'M_ADMINISTER_PROJECTS',
      'eventDelete' => 'M_ADMINISTER_PROJECTS',

      'eventEmployeeCreate' => 'M_ADMINISTER_PROJECTS',
      'eventEmployeeDelete' => 'M_ADMINISTER_PROJECTS',

      'employeeAvailabilityList'       => 'M_VIEWER',
      'employeeAvailabilityUpdate'     => 'M_ADMINISTER_PROJECTS',
      'employeeAvailabilityForProject' => 'M_VIEWER',
      'employeeAvailabilityRange'      => 'M_VIEWER',

      'searchLocations' => 'M_ADMINISTER_PROJECTS',
      'googleEventList' => 'M_VIEWER',

      'changelog' => 'M_CHANGELOG',
    ];

    public $data = [];

    public function __construct() {
      $this->data = json_decode(file_get_contents('php://input'), true);
    }

    public function preExecute(): void {
      if (!array_key_exists($this->action, self::ACTION_PERMISSION)) {
        ApiResponse::sendResponseError('Invalid action');
      }

      if (self::ACTION_PERMISSION[$this->action]) {
        if (empty($_SESSION['loggedIn']) || empty($_SESSION['userObject'])) {
          ApiResponse::sendResponseUnauthorized();
        }
        elseif (!Privilege::hasRight(self::ACTION_PERMISSION[$this->action])) {
          ApiResponse::sendAccessDeniedResponse();
        }
      }
    }
  }
